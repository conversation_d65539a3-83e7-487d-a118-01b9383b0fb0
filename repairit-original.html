<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>本地草稿页 - Repairit</title>
    
    <!-- Bootstrap 4 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Swiper 7 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@7/swiper-bundle.min.css">
    
    <style>
        /* 全局样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #ffffff;
            overflow-x: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }
        
        /* 主容器 - 严格按照Figma尺寸 690x408.17 */
        .main-frame {
            width: 690px;
            height: 408.17px;
            position: relative;
            background: #ffffff;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        /* 导航区域 */
        .navigation-section {
            position: relative;
            z-index: 10;
        }
        
        /* 主导航栏 */
        .main-navbar {
            background: #ffffff;
            box-shadow: 0 1px 15px rgba(0, 0, 0, 0.2);
            padding: 16px 32px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 12px;
            text-decoration: none;
        }
        
        .product-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #A4DFFF 0%, #2391FF 100%);
            border-radius: 7.3px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: inset 0.6px 0.6px 0.6px rgba(255, 255, 255, 1), 
                        inset -1.2px -1.2px 0px rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(12px);
        }
        
        .product-name {
            font-size: 16px;
            font-weight: 600;
            color: #1a1a1a;
            line-height: 1.14;
        }
        
        .nav-menu {
            display: flex;
            align-items: center;
            gap: 32px;
        }
        
        .nav-item {
            font-size: 14px;
            font-weight: 500;
            color: #1a1a1a;
            text-decoration: none;
            padding: 8px 0;
            position: relative;
            transition: all 0.3s ease;
        }
        
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: #0055fb;
        }
        
        .nav-buttons {
            display: flex;
            gap: 12px;
        }
        
        .btn-download {
            background: #0055fb;
            color: white;
            border: none;
            padding: 8px 24px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-buy {
            background: transparent;
            color: #0055fb;
            border: 1px solid #0055fb;
            padding: 8px 24px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }
        
        .nav-icons {
            display: flex;
            gap: 16px;
            align-items: center;
        }
        
        .nav-icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
        }
        
        /* 二级导航 */
        .secondary-nav {
            background: #1a1a1a;
            padding: 12px 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .nav-categories {
            display: flex;
            gap: 32px;
            align-items: center;
        }
        
        .nav-category {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .nav-category.active {
            background: #ffffff;
            color: #1a1a1a;
        }
        
        .company-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        /* 主要内容区域 */
        .main-content {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
            height: calc(100% - 140px);
            position: relative;
            overflow: hidden;
        }
        
        /* 纹理背景 - 严格按照设计稿 */
        .texture-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            pointer-events: none;
        }
        
        .texture-group {
            position: absolute;
        }
        
        .texture-group-1 {
            top: 20px;
            left: 50px;
        }
        
        .texture-group-2 {
            top: 30px;
            right: 80px;
        }
        
        .texture-group-3 {
            bottom: 40px;
            left: 100px;
        }
        
        .texture-group-4 {
            bottom: 20px;
            right: 60px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-frame {
                width: 100%;
                max-width: 690px;
                height: auto;
                min-height: 408px;
            }
            
            .nav-menu {
                display: none;
            }
            
            .main-navbar {
                padding: 12px 16px;
            }
            
            .secondary-nav {
                padding: 8px 16px;
                flex-direction: column;
                gap: 12px;
            }
            
            .nav-categories {
                gap: 16px;
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- 主框架 - 严格按照Figma设计 -->
    <div class="main-frame">
        <!-- 导航区域 -->
        <div class="navigation-section">
            <!-- 主导航栏 -->
            <div class="main-navbar">
                <a class="navbar-brand" href="#">
                    <div class="product-icon">
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
                            <path d="M2 2h14v14H2z" fill="#052359"/>
                            <path d="M4 6h10v6H4z" fill="#09407A"/>
                            <path d="M6 8h6v2H6z" fill="#ffffff" opacity="0.4"/>
                            <path d="M8 10h2v2H8z" fill="#ffffff"/>
                        </svg>
                    </div>
                    <span class="product-name">Repairit</span>
                </a>
                
                <div class="nav-menu">
                    <a href="#" class="nav-item active">Products</a>
                    <a href="#" class="nav-item">Features</a>
                    <a href="#" class="nav-item">Guide</a>
                    <a href="#" class="nav-item">Tips & Tricks</a>
                    <a href="#" class="nav-item">Help Center</a>
                </div>
                
                <div style="display: flex; align-items: center; gap: 16px;">
                    <div class="nav-buttons">
                        <a href="#" class="btn-download">Download</a>
                        <a href="#" class="btn-buy">Buy now</a>
                    </div>
                    
                    <div class="nav-icons">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="9" cy="21" r="1"></circle>
                            <circle cx="20" cy="21" r="1"></circle>
                            <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- 二级导航 -->
            <div class="secondary-nav">
                <div class="nav-categories">
                    <a href="#" class="nav-category">Productivity</a>
                    <a href="#" class="nav-category active">Creativity</a>
                    <a href="#" class="nav-category">Utility</a>
                    <a href="#" class="nav-category">Support</a>
                    <a href="#" class="nav-category">News</a>
                </div>
                
                <div class="company-logo">
                    <img src="repairit-images/wondershare-logomark.svg" alt="Wondershare" height="20">
                    <img src="repairit-images/wondershare-logo.svg" alt="Wondershare" height="14">
                </div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 纹理背景 -->
            <div class="texture-overlay">
                <div class="texture-group texture-group-1">
                    <img src="repairit-images/texture-vector-1.svg" alt="" style="opacity: 0.3;">
                    <img src="repairit-images/texture-vector-2.svg" alt="" style="opacity: 0.2;">
                </div>
                <div class="texture-group texture-group-2">
                    <img src="repairit-images/texture-vector-3.svg" alt="" style="opacity: 0.25;">
                    <img src="repairit-images/texture-vector-4.svg" alt="" style="opacity: 0.3;">
                </div>
                <div class="texture-group texture-group-3">
                    <img src="repairit-images/texture-vector-5.svg" alt="" style="opacity: 0.2;">
                    <img src="repairit-images/texture-vector-6.svg" alt="" style="opacity: 0.25;">
                </div>
                <div class="texture-group texture-group-4">
                    <img src="repairit-images/texture-vector-7.svg" alt="" style="opacity: 0.3;">
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    
    <!-- Bootstrap 4 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Swiper 7 JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@7/swiper-bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // 基本的导航交互
            $('.nav-item').on('click', function(e) {
                e.preventDefault();
                $('.nav-item').removeClass('active');
                $(this).addClass('active');
            });
            
            $('.nav-category').on('click', function(e) {
                e.preventDefault();
                $('.nav-category').removeClass('active');
                $(this).addClass('active');
            });
            
            // 按钮点击效果
            $('.btn-download, .btn-buy').on('click', function(e) {
                e.preventDefault();
                console.log('按钮被点击:', $(this).text());
            });
        });
    </script>
</body>
</html>
