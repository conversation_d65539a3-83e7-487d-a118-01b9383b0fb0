<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Repairit - 万兴易修</title>

    <!-- Bootstrap 4 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet" />

    <!-- Swiper 7 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@7/swiper-bundle.min.css" />

    <style>
      /* 全局样式 */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        background: #f8f9fa;
        overflow-x: hidden;
      }

      /* 导航栏样式 */
      .main-navbar {
        background: #ffffff;
        box-shadow: 0 1px 15px rgba(0, 0, 0, 0.2);
        position: relative;
        z-index: 1000;
      }

      .navbar-brand {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .product-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #a4dfff 0%, #2391ff 100%);
        border-radius: 7px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        box-shadow: inset 1px 1px 1px rgba(255, 255, 255, 1), inset -2px -2px 0px rgba(255, 255, 255, 0.3);
      }

      .product-name {
        font-size: 16px;
        font-weight: 600;
        color: #1a1a1a;
      }

      .nav-menu {
        display: flex;
        align-items: center;
        gap: 32px;
      }

      .nav-item-custom {
        font-size: 14px;
        font-weight: 500;
        color: #1a1a1a;
        text-decoration: none;
        padding: 8px 0;
        position: relative;
        transition: all 0.3s ease;
      }

      .nav-item-custom:hover {
        color: #0055fb;
        text-decoration: none;
      }

      .nav-item-custom.active::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: #0055fb;
      }

      .nav-buttons {
        display: flex;
        gap: 12px;
      }

      .btn-download {
        background: #0055fb;
        color: white;
        border: none;
        padding: 8px 24px;
        border-radius: 6px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .btn-download:hover {
        background: #0044cc;
        color: white;
        transform: translateY(-1px);
      }

      .btn-buy {
        background: transparent;
        color: #0055fb;
        border: 1px solid #0055fb;
        padding: 8px 24px;
        border-radius: 6px;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .btn-buy:hover {
        background: #0055fb;
        color: white;
      }

      .nav-icons {
        display: flex;
        gap: 16px;
        align-items: center;
      }

      .nav-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;
        transition: transform 0.3s ease;
      }

      .nav-icon:hover {
        transform: scale(1.1);
      }

      /* 二级导航 */
      .secondary-nav {
        background: #1a1a1a;
        padding: 12px 0;
      }

      .nav-categories {
        display: flex;
        gap: 32px;
        align-items: center;
      }

      .nav-category {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        font-weight: 500;
        text-decoration: none;
        padding: 8px 16px;
        border-radius: 4px;
        transition: all 0.3s ease;
      }

      .nav-category.active {
        background: #ffffff;
        color: #1a1a1a;
      }

      .nav-category:hover {
        color: #ffffff;
        text-decoration: none;
      }

      .company-logo {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      /* 主要内容区域 */
      .main-content {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
        min-height: calc(100vh - 140px);
        position: relative;
        overflow: hidden;
      }

      .texture-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0.1;
        pointer-events: none;
        background-image: url("repairit-images/texture-vector-1.svg"), url("repairit-images/texture-vector-2.svg"), url("repairit-images/texture-vector-3.svg");
        background-position: 10% 20%, 80% 30%, 50% 70%;
        background-repeat: no-repeat;
        background-size: 150px auto, 100px auto, 120px auto;
        animation: textureFloat 20s ease-in-out infinite;
      }

      @keyframes textureFloat {
        0%,
        100% {
          transform: translateY(0px) rotate(0deg);
        }
        25% {
          transform: translateY(-10px) rotate(1deg);
        }
        50% {
          transform: translateY(-5px) rotate(-1deg);
        }
        75% {
          transform: translateY(-15px) rotate(0.5deg);
        }
      }

      .content-wrapper {
        position: relative;
        z-index: 10;
        padding: 60px 0;
      }

      .product-showcase {
        display: flex;
        align-items: center;
        gap: 60px;
      }

      .product-info {
        flex: 1;
        color: white;
      }

      .product-title {
        font-size: 48px;
        font-weight: 700;
        margin-bottom: 24px;
        background: linear-gradient(135deg, #ffffff 0%, #a0aec0 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .product-description {
        font-size: 18px;
        line-height: 1.6;
        margin-bottom: 32px;
        color: rgba(255, 255, 255, 0.8);
      }

      .feature-list {
        list-style: none;
        margin-bottom: 40px;
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
      }

      .feature-icon {
        width: 20px;
        height: 20px;
        background: #23b939;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .product-visual {
        flex: 1;
        position: relative;
      }

      .product-image {
        width: 100%;
        max-width: 400px;
        height: auto;
        border-radius: 12px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      }

      /* 底部操作区域 */
      .action-bar {
        background: white;
        box-shadow: 0 -1px 15px rgba(0, 0, 0, 0.2);
        padding: 20px 0;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
      }

      .action-content {
        display: flex;
        align-items: center;
        gap: 16px;
      }

      .status-indicators {
        display: flex;
        gap: 16px;
        flex: 1;
      }

      .status-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 16px;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 600;
      }

      .status-success {
        background: rgba(0, 85, 251, 0.1);
        color: #0055fb;
        border: 1px solid rgba(0, 85, 251, 0.24);
      }

      .status-error {
        background: rgba(248, 48, 60, 0.1);
        color: #f8303c;
        border: 1px solid rgba(248, 48, 60, 0.24);
      }

      .repair-button {
        background: linear-gradient(135deg, #8acfff 0%, #4d89ff 100%);
        color: white;
        border: none;
        padding: 12px 48px;
        border-radius: 8px;
        font-weight: 700;
        font-size: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(77, 137, 255, 0.3);
      }

      .repair-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(77, 137, 255, 0.4);
        color: white;
      }

      /* 动画效果 */
      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }

      .product-info {
        animation: fadeInUp 1s ease-out;
      }

      .product-visual {
        animation: fadeInUp 1s ease-out 0.3s both;
      }

      .feature-item {
        animation: fadeInUp 0.6s ease-out both;
      }

      .feature-item:nth-child(1) {
        animation-delay: 0.1s;
      }
      .feature-item:nth-child(2) {
        animation-delay: 0.2s;
      }
      .feature-item:nth-child(3) {
        animation-delay: 0.3s;
      }

      .repair-button:hover {
        animation: pulse 0.6s ease-in-out;
      }

      .product-icon svg {
        transition: transform 0.3s ease;
      }

      .navbar-brand:hover .product-icon svg {
        transform: rotate(5deg) scale(1.1);
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .nav-menu {
          display: none;
        }

        .product-showcase {
          flex-direction: column;
          gap: 40px;
        }

        .product-title {
          font-size: 32px;
        }

        .action-content {
          flex-direction: column;
          gap: 12px;
        }

        .status-indicators {
          justify-content: center;
        }

        .texture-overlay {
          background-size: 100px auto, 80px auto, 90px auto;
        }
      }
    </style>
  </head>
  <body>
    <!-- 主导航栏 -->
    <nav class="navbar navbar-expand-lg main-navbar">
      <div class="container">
        <a class="navbar-brand" href="#">
          <div class="product-icon">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none">
              <path d="M2 2h14v14H2z" fill="#052359" />
              <path d="M4 6h10v6H4z" fill="#09407A" />
              <path d="M6 8h6v2H6z" fill="#ffffff" opacity="0.4" />
              <path d="M8 10h2v2H8z" fill="#ffffff" />
            </svg>
          </div>
          <span class="product-name">Repairit</span>
        </a>

        <div class="nav-menu">
          <a href="#" class="nav-item-custom active">Products</a>
          <a href="#" class="nav-item-custom">Features</a>
          <a href="#" class="nav-item-custom">Guide</a>
          <a href="#" class="nav-item-custom">Tips & Tricks</a>
          <a href="#" class="nav-item-custom">Help Center</a>
        </div>

        <div class="d-flex align-items-center gap-3">
          <div class="nav-buttons">
            <button class="btn btn-download">Download</button>
            <button class="btn btn-buy">Buy now</button>
          </div>

          <div class="nav-icons">
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="9" cy="21" r="1"></circle>
              <circle cx="20" cy="21" r="1"></circle>
              <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
            </svg>
          </div>
        </div>
      </div>
    </nav>

    <!-- 二级导航 -->
    <div class="secondary-nav">
      <div class="container">
        <div class="d-flex justify-content-between align-items-center">
          <div class="nav-categories">
            <a href="#" class="nav-category">Productivity</a>
            <a href="#" class="nav-category active">Creativity</a>
            <a href="#" class="nav-category">Utility</a>
            <a href="#" class="nav-category">Support</a>
            <a href="#" class="nav-category">News</a>
          </div>

          <div class="company-logo">
            <img src="repairit-images/wondershare-logomark.svg" alt="Wondershare" height="20" />
            <img src="repairit-images/wondershare-logo.svg" alt="Wondershare" height="14" />
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="texture-overlay">
        <!-- 背景纹理元素 -->
      </div>

      <div class="container content-wrapper">
        <div class="product-showcase">
          <div class="product-info">
            <h1 class="product-title">视频修复专家</h1>
            <p class="product-description">专业的视频修复工具，支持多种视频格式的损坏修复， 让您的珍贵视频重获新生。</p>

            <ul class="feature-list">
              <li class="feature-item">
                <div class="feature-icon">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="white">
                    <path d="M10 3L4.5 8.5L2 6" />
                  </svg>
                </div>
                支持多种视频格式修复
              </li>
              <li class="feature-item">
                <div class="feature-icon">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="white">
                    <path d="M10 3L4.5 8.5L2 6" />
                  </svg>
                </div>
                高成功率的修复算法
              </li>
              <li class="feature-item">
                <div class="feature-icon">
                  <svg width="12" height="12" viewBox="0 0 12 12" fill="white">
                    <path d="M10 3L4.5 8.5L2 6" />
                  </svg>
                </div>
                简单易用的操作界面
              </li>
            </ul>
          </div>

          <div class="product-visual">
            <img src="repairit-images/background-image-1.png" alt="Repairit Interface" class="product-image" />
          </div>
        </div>
      </div>
    </main>

    <!-- 底部操作栏 -->
    <div class="action-bar">
      <div class="container">
        <div class="action-content">
          <div class="status-indicators">
            <div class="status-item status-success">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <circle cx="8" cy="8" r="8" />
              </svg>
              已修复: 5 个文件
            </div>
            <div class="status-item status-error">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                <circle cx="8" cy="8" r="8" />
              </svg>
              损坏: 2 个文件
            </div>
          </div>

          <button class="repair-button">开始修复</button>
        </div>
      </div>
    </div>

    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>

    <!-- Bootstrap 4 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Swiper 7 JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@7/swiper-bundle.min.js"></script>

    <script>
      $(document).ready(function () {
        // 导航菜单交互效果
        $(".nav-item-custom")
          .on("mouseenter", function () {
            $(this).addClass("hover");
          })
          .on("mouseleave", function () {
            $(this).removeClass("hover");
          });

        // 按钮点击效果
        $(".btn-download, .btn-buy, .repair-button").on("click", function (e) {
          e.preventDefault();

          // 添加点击动画效果
          $(this).addClass("clicked");
          setTimeout(() => {
            $(this).removeClass("clicked");
          }, 200);

          console.log("按钮被点击:", $(this).text());
        });

        // 状态指示器动画
        $(".status-item").each(function (index) {
          $(this).css("animation-delay", index * 0.1 + "s");
        });

        // 产品图片悬停效果
        $(".product-image")
          .on("mouseenter", function () {
            $(this).css("transform", "scale(1.05) rotateY(5deg)");
          })
          .on("mouseleave", function () {
            $(this).css("transform", "scale(1) rotateY(0deg)");
          });

        // 添加页面加载动画
        $("body").addClass("loaded");

        // 滚动效果
        $(window).on("scroll", function () {
          const scrollTop = $(this).scrollTop();

          // 导航栏阴影效果
          if (scrollTop > 10) {
            $(".main-navbar").addClass("scrolled");
          } else {
            $(".main-navbar").removeClass("scrolled");
          }
        });
      });
    </script>
  </body>
</html>
