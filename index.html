<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>How to Fix Corrupted GoPro Video Files</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@7.4.1/swiper-bundle.min.css">
    <style>
        /* 自定义样式 */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 0;
        }

        /* Mac风格顶部导航栏 */
        .mac-navigation {
            background: #ffffff;
            padding: 12px 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .mac-controls {
            display: flex;
            gap: 8px;
        }

        .mac-control-btn {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
        }

        .mac-control-close { background: #ff5f57; }
        .mac-control-minimize { background: #ffbd2e; }
        .mac-control-maximize { background: #28ca42; }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-text {
            font-weight: 600;
            font-size: 14px;
            color: #333;
        }

        .nav-icons {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .nav-icon:hover {
            opacity: 1;
        }

        .pro-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
        }

        /* 主要内容区域 */
        .main-content {
            max-width: 690px;
            margin: 40px auto;
            padding: 0 20px;
        }

        .content-header {
            text-align: left;
            margin-bottom: 32px;
        }

        .main-title {
            font-size: 28px;
            font-weight: 800;
            color: #1a1a1a;
            margin-bottom: 16px;
            line-height: 1.3;
        }

        .main-description {
            font-size: 16px;
            color: rgba(0, 0, 0, 0.7);
            line-height: 1.6;
            margin-bottom: 0;
        }

        /* 选项卡样式 */
        .repair-options {
            display: flex;
            gap: 12px;
            margin-bottom: 32px;
            flex-wrap: wrap;
        }

        .repair-option {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            flex: 1;
            min-width: 180px;
        }

        .repair-option.active {
            background: linear-gradient(135deg, #4d89ff 0%, #6fcaff 100%);
            color: white;
        }

        .repair-option.inactive {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #e9ecef;
        }

        .repair-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* 软件预览卡片 */
        .software-preview-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 2px solid #4d89ff;
        }

        .preview-image {
            width: 100%;
            height: 400px;
            object-fit: cover;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .repair-options {
                flex-direction: column;
            }
            
            .repair-option {
                min-width: auto;
            }
            
            .main-title {
                font-size: 24px;
            }
            
            .nav-icons {
                gap: 12px;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* 渐变背景 */
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 0;
        }
    </style>
</head>
<body>
    <!-- Mac风格导航栏 -->
    <div class="mac-navigation">
        <div class="mac-controls">
            <button class="mac-control-btn mac-control-close"></button>
            <button class="mac-control-btn mac-control-minimize"></button>
            <button class="mac-control-btn mac-control-maximize"></button>
        </div>
        
        <div class="logo-section">
            <span class="logo-text">Wondershare Repairit</span>
            <span class="pro-badge">PRO</span>
        </div>
        
        <div class="nav-icons">
            <img src="images/icon-avatar.svg" alt="用户头像" class="nav-icon">
            <img src="images/icon-message.svg" alt="消息" class="nav-icon">
            <img src="images/icon-support.svg" alt="支持" class="nav-icon">
            <img src="images/icon-menu.svg" alt="菜单" class="nav-icon">
        </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 内容标题 -->
        <div class="content-header fade-in-up">
            <h1 class="main-title">How to Fix Corrupted GoPro Video Files</h1>
            <p class="main-description">
                Here are three reliable ways to repair broken GoPro footage, each with its pros and cons, 
                so you can choose what works best for your situation.
            </p>
        </div>

        <!-- 修复选项 -->
        <div class="repair-options fade-in-up">
            <button class="repair-option active" data-option="repairit">
                Repairit Video Repair Tool
            </button>
            <button class="repair-option inactive" data-option="sos">
                GoPro's SOS Function
            </button>
            <button class="repair-option inactive" data-option="vlc">
                VLC Media Player
            </button>
        </div>

        <!-- 软件预览 -->
        <div class="software-preview-card fade-in-up">
            <img src="images/main-background-image.png" alt="Repairit软件界面预览" class="preview-image">
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@7.4.1/swiper-bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // 选项卡切换功能
            $('.repair-option').on('click', function() {
                // 移除所有活动状态
                $('.repair-option').removeClass('active').addClass('inactive');
                
                // 为当前点击的选项添加活动状态
                $(this).removeClass('inactive').addClass('active');
                
                // 获取选项类型
                const optionType = $(this).data('option');
                
                // 根据选项类型可以切换不同的内容
                console.log('选择的修复方式:', optionType);
                
                // 添加点击动画效果
                $(this).css('transform', 'scale(0.95)');
                setTimeout(() => {
                    $(this).css('transform', '');
                }, 150);
            });

            // Mac控制按钮功能
            $('.mac-control-close').on('click', function() {
                if (confirm('确定要关闭窗口吗？')) {
                    window.close();
                }
            });

            $('.mac-control-minimize').on('click', function() {
                // 模拟最小化效果
                $('body').animate({opacity: 0.3}, 300).animate({opacity: 1}, 300);
            });

            $('.mac-control-maximize').on('click', function() {
                // 模拟最大化效果
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    document.documentElement.requestFullscreen();
                }
            });

            // 导航图标悬停效果
            $('.nav-icon').on('mouseenter', function() {
                $(this).css('transform', 'scale(1.1)');
            }).on('mouseleave', function() {
                $(this).css('transform', 'scale(1)');
            });

            // 页面加载动画
            setTimeout(() => {
                $('.fade-in-up').each(function(index) {
                    setTimeout(() => {
                        $(this).css('animation-delay', index * 0.1 + 's');
                    }, index * 100);
                });
            }, 100);
        });
    </script>
</body>
</html>
