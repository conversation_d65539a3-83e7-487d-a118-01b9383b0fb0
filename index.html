<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Repairit - 万兴易修</title>
    <!-- Bootstrap 4 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Swiper.js v7 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@7/swiper-bundle.min.css">
    <style>
        /* 桌面优先样式 */
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8f9fa;
        }

        /* 导航栏样式 */
        .main-navbar {
            background: #ffffff;
            box-shadow: 0 1px 16px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1000;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .product-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #A4DFFF 0%, #2391FF 100%);
            border-radius: 7px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: inset 1px 1px 1px rgba(255, 255, 255, 1), 
                        inset -2px -2px 0px rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(12px);
        }

        .product-name {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 32px;
        }

        .nav-item-custom {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            text-decoration: none;
            padding: 8px 0;
            position: relative;
            transition: color 0.3s ease;
        }

        .nav-item-custom:hover {
            color: #2391FF;
            text-decoration: none;
        }

        .nav-item-custom.active {
            color: #2391FF;
        }

        .nav-item-custom.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: #2391FF;
        }

        .btn-group-nav {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn-download {
            background: #2391FF;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-download:hover {
            background: #1a7ae6;
            color: white;
            transform: translateY(-1px);
        }

        .btn-buy {
            background: transparent;
            color: #2391FF;
            border: 1px solid #2391FF;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-buy:hover {
            background: #2391FF;
            color: white;
        }

        .icon-group {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .nav-icon:hover {
            transform: scale(1.1);
        }

        /* 二级导航 */
        .secondary-nav {
            background: #333;
            color: white;
            padding: 12px 0;
        }

        .nav-category {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-right: 32px;
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .nav-category.active {
            color: white;
            position: relative;
        }

        .nav-category:hover {
            color: white;
        }

        .wondershare-logo {
            height: 20px;
        }

        /* 主要内容区域 */
        .main-content {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        .texture-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0.1;
            pointer-events: none;
        }

        .content-wrapper {
            position: relative;
            z-index: 2;
            padding: 80px 0;
        }

        /* 响应式设计 - 平板 */
        @media (max-width: 992px) {
            .nav-menu {
                gap: 20px;
            }
            
            .nav-item-custom {
                font-size: 13px;
            }
        }

        /* 响应式设计 - 手机 */
        @media (max-width: 768px) {
            .navbar-brand {
                gap: 8px;
            }
            
            .product-name {
                font-size: 12px;
            }
            
            .nav-menu {
                display: none;
            }
            
            .btn-group-nav {
                gap: 8px;
            }
            
            .btn-download, .btn-buy {
                padding: 6px 12px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- 主导航栏 -->
    <nav class="navbar navbar-expand-lg main-navbar">
        <div class="container-fluid px-4">
            <!-- Logo区域 -->
            <div class="navbar-brand">
                <div class="product-icon">
                    <img src="images/product-icon-path-1.svg" alt="Repairit Icon" style="width: 20px; height: 20px;">
                </div>
                <h1 class="product-name">Repairit</h1>
            </div>

            <!-- 导航菜单 -->
            <div class="nav-menu d-none d-lg-flex">
                <a href="#" class="nav-item-custom active">Products</a>
                <a href="#" class="nav-item-custom">Features</a>
                <a href="#" class="nav-item-custom">Guide</a>
                <a href="#" class="nav-item-custom">Tips & Tricks</a>
                <a href="#" class="nav-item-custom">Help Center</a>
            </div>

            <!-- 按钮组和图标 -->
            <div class="d-flex align-items-center">
                <div class="btn-group-nav">
                    <button class="btn btn-download">Download</button>
                    <button class="btn btn-buy">Buy now</button>
                </div>
                <div class="icon-group ml-3">
                    <img src="images/search-icon.svg" alt="Search" class="nav-icon">
                    <div class="position-relative">
                        <img src="images/cart-icon-1.svg" alt="Cart" class="nav-icon">
                        <img src="images/cart-icon-2.svg" alt="Cart Badge" class="position-absolute" style="top: -2px; right: -2px; width: 12px; height: 3px;">
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 二级导航 -->
    <div class="secondary-nav">
        <div class="container-fluid px-4">
            <div class="d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <span class="nav-category">Productivity</span>
                    <span class="nav-category active">Creativity</span>
                    <span class="nav-category">Utility</span>
                    <span class="nav-category">Support</span>
                    <span class="nav-category">News</span>
                </div>
                <div class="d-flex align-items-center">
                    <img src="images/wondershare-logomark.svg" alt="Wondershare" class="wondershare-logo mr-2">
                    <img src="images/wondershare-logo.svg" alt="Wondershare" class="wondershare-logo">
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 背景纹理 -->
        <div class="texture-overlay">
            <img src="images/texture-vector-1.svg" alt="" style="position: absolute; top: 10%; left: 5%;">
            <img src="images/texture-vector-2.svg" alt="" style="position: absolute; top: 20%; right: 10%;">
            <img src="images/texture-vector-3.svg" alt="" style="position: absolute; bottom: 15%; left: 15%;">
            <img src="images/texture-ellipse-1.svg" alt="" style="position: absolute; top: 30%; left: 30%;">
            <img src="images/texture-ellipse-2.svg" alt="" style="position: absolute; bottom: 25%; right: 25%;">
        </div>

        <div class="content-wrapper">
            <div class="container">
                <!-- 这里可以添加更多内容 -->
                <div class="text-center text-white">
                    <h2>Repairit - 专业视频修复工具</h2>
                    <p class="lead">修复损坏的视频文件，恢复珍贵回忆</p>
                </div>
            </div>
        </div>
    </main>

    <!-- jQuery -->
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <!-- Bootstrap 4 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Swiper.js v7 -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@7/swiper-bundle.min.js"></script>

    <script>
        $(document).ready(function() {
            // 导航项点击效果
            $('.nav-item-custom').on('click', function(e) {
                e.preventDefault();
                $('.nav-item-custom').removeClass('active');
                $(this).addClass('active');
            });

            // 按钮悬停效果增强
            $('.btn-download, .btn-buy').hover(
                function() {
                    $(this).addClass('shadow-sm');
                },
                function() {
                    $(this).removeClass('shadow-sm');
                }
            );

            // 图标点击效果
            $('.nav-icon').on('click', function() {
                $(this).addClass('animate__pulse');
                setTimeout(() => {
                    $(this).removeClass('animate__pulse');
                }, 600);
            });

            // 二级导航分类切换
            $('.nav-category').on('click', function() {
                $('.nav-category').removeClass('active');
                $(this).addClass('active');
            });
        });
    </script>
</body>
</html>
